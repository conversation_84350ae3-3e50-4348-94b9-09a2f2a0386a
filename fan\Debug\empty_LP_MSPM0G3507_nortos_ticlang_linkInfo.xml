<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.0.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\bin\tiarmlnk -ID:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib -o empty_LP_MSPM0G3507_nortos_ticlang.out -mempty_LP_MSPM0G3507_nortos_ticlang.map -iD:/TI/CCSTUDIO_Theia/mspm0_sdk_2_04_00_06/source -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang/Debug/syscfg -iD:/TI/CCSTUDIO_Theia/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_LP_MSPM0G3507_nortos_ticlang_linkInfo.xml --rom_model ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./main.o ./APP/Src/Task_App.o ./BSP/Src/Key_Led.o ./BSP/Src/Motor.o ./BSP/Src/SysTick.o ./BSP/Src/Task.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x686b9289</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0xe41</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\APP\Src\</path>
         <kind>object</kind>
         <file>Task_App.o</file>
         <name>Task_App.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\BSP\Src\</path>
         <kind>object</kind>
         <file>Key_Led.o</file>
         <name>Key_Led.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\BSP\Src\</path>
         <kind>object</kind>
         <file>Motor.o</file>
         <name>Motor.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\BSP\Src\</path>
         <kind>object</kind>
         <file>SysTick.o</file>
         <name>SysTick.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\BSP\Src\</path>
         <kind>object</kind>
         <file>Task.o</file>
         <name>Task.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\TI\CCSTUDIO_Theia\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\TI\CCSTUDIO_Theia\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-18">
         <path>D:\TI\CCSTUDIO_Theia\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-2d">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>qsort.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-ce">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-cf">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d0">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-d1">
         <path>D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.text.Task_Start</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x1b0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-112">
         <name>.text.qsort</name>
         <load_address>0x270</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x270</run_address>
         <size>0x134</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x3a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3a4</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0x4a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4a8</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.Motor_SetPWM</name>
         <load_address>0x584</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x584</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.Task_Add</name>
         <load_address>0x648</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x648</run_address>
         <size>0xb4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x6fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6fc</run_address>
         <size>0x94</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text.SYSCFG_DL_MotorBack_init</name>
         <load_address>0x790</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x790</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text.SYSCFG_DL_MotorFront_init</name>
         <load_address>0x81c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x81c</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.text.Task_Key</name>
         <load_address>0x8a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8a8</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-131">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x930</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x930</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x9b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9b4</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.text.DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <load_address>0xa30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa30</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-89">
         <name>.text.Task_Init</name>
         <load_address>0xa94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa94</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xaf0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaf0</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.text.SysTick_Config</name>
         <load_address>0xb48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb48</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xb98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb98</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0xbe0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbe0</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-be">
         <name>.text.Motor_Start</name>
         <load_address>0xc24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc24</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-116">
         <name>.text.Task_CMP</name>
         <load_address>0xc64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc64</run_address>
         <size>0x3e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.Task_IdleFunction</name>
         <load_address>0xca2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xca2</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0xca4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xca4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xce0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xce0</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text.Task_LED</name>
         <load_address>0xd1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd1c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text.Task_Motor</name>
         <load_address>0xd54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd54</run_address>
         <size>0x36</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0xd8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd8c</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-117">
         <name>.text.Key_Read</name>
         <load_address>0xdc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdc0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0xdf0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdf0</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0xe18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe18</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text:_c_int00_noargs</name>
         <load_address>0xe40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe40</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.DL_SYSCTL_setFlashWaitState</name>
         <load_address>0xe68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe68</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.text.main</name>
         <load_address>0xe88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe88</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0xea8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xea8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0xec4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xec4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0xee0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xee0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0xefc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xefc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0xf18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf18</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-84">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0xf34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf34</run_address>
         <size>0x1c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0xf50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf50</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0xf68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf68</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0xf80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf80</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.DL_GPIO_initPeripheralAnalogFunction</name>
         <load_address>0xf98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf98</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0xfb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfb0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0xfc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfc8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0xfe0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfe0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0xff8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xff8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x1010</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1010</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1028</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1028</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.DL_Timer_startCounter</name>
         <load_address>0x1040</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1040</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-135">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1058</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1058</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x106e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x106e</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1084</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1084</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1098</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1098</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x10ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10ac</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x10c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10c0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x10d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10d4</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x10e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10e8</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x10fa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10fa</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x110c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x110c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x111c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x111c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x112c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x112c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text.Sys_GetTick</name>
         <load_address>0x113c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x113c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x1148</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1148</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-44">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x1154</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1154</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x115c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x115c</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x1160</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1160</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-68">
         <name>.text._system_pre_init</name>
         <load_address>0x1164</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1164</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text:abort</name>
         <load_address>0x1168</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1168</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-177">
         <name>__TI_handler_table</name>
         <load_address>0x11c0</load_address>
         <readonly>true</readonly>
         <run_address>0x11c0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-179">
         <name>.cinit..data.load</name>
         <load_address>0x11cc</load_address>
         <readonly>true</readonly>
         <run_address>0x11cc</run_address>
         <size>0xb</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-17a">
         <name>.cinit..bss.load</name>
         <load_address>0x11d8</load_address>
         <readonly>true</readonly>
         <run_address>0x11d8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-178">
         <name>__TI_cinit_table</name>
         <load_address>0x11e0</load_address>
         <readonly>true</readonly>
         <run_address>0x11e0</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-fb">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x1170</load_address>
         <readonly>true</readonly>
         <run_address>0x1170</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.rodata.gMotorBackConfig</name>
         <load_address>0x1198</load_address>
         <readonly>true</readonly>
         <run_address>0x1198</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.rodata.gMotorFrontConfig</name>
         <load_address>0x11a0</load_address>
         <readonly>true</readonly>
         <run_address>0x11a0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.rodata.str1.115332825834609149281</name>
         <load_address>0x11a8</load_address>
         <readonly>true</readonly>
         <run_address>0x11a8</run_address>
         <size>0x6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.rodata.str1.171900814140190138471</name>
         <load_address>0x11ae</load_address>
         <readonly>true</readonly>
         <run_address>0x11ae</run_address>
         <size>0x4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.rodata.str1.67400646179352630301</name>
         <load_address>0x11b2</load_address>
         <readonly>true</readonly>
         <run_address>0x11b2</run_address>
         <size>0x4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.rodata.gMotorBackClockConfig</name>
         <load_address>0x11b6</load_address>
         <readonly>true</readonly>
         <run_address>0x11b6</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-109">
         <name>.rodata.gMotorFrontClockConfig</name>
         <load_address>0x11b9</load_address>
         <readonly>true</readonly>
         <run_address>0x11b9</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-141">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-11d">
         <name>.data.Flag_LED</name>
         <load_address>0x202001ec</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001ec</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.data.Data_MotorPWM</name>
         <load_address>0x202001e0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e0</run_address>
         <size>0x4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.data.Task_Key.Key_Old</name>
         <load_address>0x202001ed</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001ed</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.data.uwTick</name>
         <load_address>0x202001e8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.data.delayTick</name>
         <load_address>0x202001e4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.data.Task_Num</name>
         <load_address>0x202001ee</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001ee</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.bss.Task_Schedule</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x1e0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_abbrev</name>
         <load_address>0x1c2</load_address>
         <run_address>0x1c2</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_abbrev</name>
         <load_address>0x22f</load_address>
         <run_address>0x22f</run_address>
         <size>0x47</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_abbrev</name>
         <load_address>0x276</load_address>
         <run_address>0x276</run_address>
         <size>0x151</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_abbrev</name>
         <load_address>0x3c7</load_address>
         <run_address>0x3c7</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_abbrev</name>
         <load_address>0x4bc</load_address>
         <run_address>0x4bc</run_address>
         <size>0x12e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_abbrev</name>
         <load_address>0x5ea</load_address>
         <run_address>0x5ea</run_address>
         <size>0x9d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_abbrev</name>
         <load_address>0x687</load_address>
         <run_address>0x687</run_address>
         <size>0x175</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_abbrev</name>
         <load_address>0x7fc</load_address>
         <run_address>0x7fc</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_abbrev</name>
         <load_address>0x85e</load_address>
         <run_address>0x85e</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_abbrev</name>
         <load_address>0xae4</load_address>
         <run_address>0xae4</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_abbrev</name>
         <load_address>0xcfc</load_address>
         <run_address>0xcfc</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0xdf4</load_address>
         <run_address>0xdf4</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_abbrev</name>
         <load_address>0xea3</load_address>
         <run_address>0xea3</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_abbrev</name>
         <load_address>0x1013</load_address>
         <run_address>0x1013</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_abbrev</name>
         <load_address>0x104c</load_address>
         <run_address>0x104c</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_abbrev</name>
         <load_address>0x110e</load_address>
         <run_address>0x110e</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0x117e</load_address>
         <run_address>0x117e</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_abbrev</name>
         <load_address>0x120b</load_address>
         <run_address>0x120b</run_address>
         <size>0xb3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_abbrev</name>
         <load_address>0x12be</load_address>
         <run_address>0x12be</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_abbrev</name>
         <load_address>0x12e5</load_address>
         <run_address>0x12e5</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.debug_abbrev</name>
         <load_address>0x130a</load_address>
         <run_address>0x130a</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2242</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x2242</load_address>
         <run_address>0x2242</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0x22c2</load_address>
         <run_address>0x22c2</run_address>
         <size>0x65</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_info</name>
         <load_address>0x2327</load_address>
         <run_address>0x2327</run_address>
         <size>0x910</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_info</name>
         <load_address>0x2c37</load_address>
         <run_address>0x2c37</run_address>
         <size>0x729</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_info</name>
         <load_address>0x3360</load_address>
         <run_address>0x3360</run_address>
         <size>0x7cc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x3b2c</load_address>
         <run_address>0x3b2c</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_info</name>
         <load_address>0x3be8</load_address>
         <run_address>0x3be8</run_address>
         <size>0x4cf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_info</name>
         <load_address>0x40b7</load_address>
         <run_address>0x40b7</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_info</name>
         <load_address>0x412c</load_address>
         <run_address>0x412c</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_info</name>
         <load_address>0x729e</load_address>
         <run_address>0x729e</run_address>
         <size>0x1090</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_info</name>
         <load_address>0x832e</load_address>
         <run_address>0x832e</run_address>
         <size>0x181</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x84af</load_address>
         <run_address>0x84af</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_info</name>
         <load_address>0x88d2</load_address>
         <run_address>0x88d2</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_info</name>
         <load_address>0x9016</load_address>
         <run_address>0x9016</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_info</name>
         <load_address>0x905c</load_address>
         <run_address>0x905c</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_info</name>
         <load_address>0x91ee</load_address>
         <run_address>0x91ee</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0x92b4</load_address>
         <run_address>0x92b4</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_info</name>
         <load_address>0x9430</load_address>
         <run_address>0x9430</run_address>
         <size>0xed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_info</name>
         <load_address>0x951d</load_address>
         <run_address>0x951d</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_info</name>
         <load_address>0x96b6</load_address>
         <run_address>0x96b6</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.debug_info</name>
         <load_address>0x99b0</load_address>
         <run_address>0x99b0</run_address>
         <size>0xbe</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0xf0</load_address>
         <run_address>0xf0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_ranges</name>
         <load_address>0x108</load_address>
         <run_address>0x108</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_ranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_ranges</name>
         <load_address>0x158</load_address>
         <run_address>0x158</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_ranges</name>
         <load_address>0x178</load_address>
         <run_address>0x178</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_ranges</name>
         <load_address>0x198</load_address>
         <run_address>0x198</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_ranges</name>
         <load_address>0x1e8</load_address>
         <run_address>0x1e8</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_ranges</name>
         <load_address>0x3c0</load_address>
         <run_address>0x3c0</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x568</load_address>
         <run_address>0x568</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_ranges</name>
         <load_address>0x5b0</load_address>
         <run_address>0x5b0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_ranges</name>
         <load_address>0x5f8</load_address>
         <run_address>0x5f8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x610</load_address>
         <run_address>0x610</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_ranges</name>
         <load_address>0x660</load_address>
         <run_address>0x660</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_ranges</name>
         <load_address>0x678</load_address>
         <run_address>0x678</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1efa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_str</name>
         <load_address>0x1efa</load_address>
         <run_address>0x1efa</run_address>
         <size>0x184</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_str</name>
         <load_address>0x207e</load_address>
         <run_address>0x207e</run_address>
         <size>0xfa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_str</name>
         <load_address>0x2178</load_address>
         <run_address>0x2178</run_address>
         <size>0x57b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_str</name>
         <load_address>0x26f3</load_address>
         <run_address>0x26f3</run_address>
         <size>0x48e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_str</name>
         <load_address>0x2b81</load_address>
         <run_address>0x2b81</run_address>
         <size>0x501</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_str</name>
         <load_address>0x3082</load_address>
         <run_address>0x3082</run_address>
         <size>0x130</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_str</name>
         <load_address>0x31b2</load_address>
         <run_address>0x31b2</run_address>
         <size>0x33f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_str</name>
         <load_address>0x34f1</load_address>
         <run_address>0x34f1</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_str</name>
         <load_address>0x365e</load_address>
         <run_address>0x365e</run_address>
         <size>0x1dcb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_str</name>
         <load_address>0x5429</load_address>
         <run_address>0x5429</run_address>
         <size>0x1075</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-133">
         <name>.debug_str</name>
         <load_address>0x649e</load_address>
         <run_address>0x649e</run_address>
         <size>0x154</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x65f2</load_address>
         <run_address>0x65f2</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_str</name>
         <load_address>0x6817</load_address>
         <run_address>0x6817</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_str</name>
         <load_address>0x6b46</load_address>
         <run_address>0x6b46</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_str</name>
         <load_address>0x6c3b</load_address>
         <run_address>0x6c3b</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_str</name>
         <load_address>0x6dd6</load_address>
         <run_address>0x6dd6</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0x6f3e</load_address>
         <run_address>0x6f3e</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_str</name>
         <load_address>0x7113</load_address>
         <run_address>0x7113</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_frame</name>
         <load_address>0x2b8</load_address>
         <run_address>0x2b8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_frame</name>
         <load_address>0x2e8</load_address>
         <run_address>0x2e8</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_frame</name>
         <load_address>0x314</load_address>
         <run_address>0x314</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_frame</name>
         <load_address>0x3bc</load_address>
         <run_address>0x3bc</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_frame</name>
         <load_address>0x3fc</load_address>
         <run_address>0x3fc</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0x458</load_address>
         <run_address>0x458</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_frame</name>
         <load_address>0x49c</load_address>
         <run_address>0x49c</run_address>
         <size>0xd0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_frame</name>
         <load_address>0x56c</load_address>
         <run_address>0x56c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_frame</name>
         <load_address>0x58c</load_address>
         <run_address>0x58c</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_frame</name>
         <load_address>0x994</load_address>
         <run_address>0x994</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_frame</name>
         <load_address>0xac0</load_address>
         <run_address>0xac0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_frame</name>
         <load_address>0xaf0</load_address>
         <run_address>0xaf0</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_frame</name>
         <load_address>0xb80</load_address>
         <run_address>0xb80</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_frame</name>
         <load_address>0xc80</load_address>
         <run_address>0xc80</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_frame</name>
         <load_address>0xca0</load_address>
         <run_address>0xca0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0xcd8</load_address>
         <run_address>0xcd8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0xd00</load_address>
         <run_address>0xd00</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_frame</name>
         <load_address>0xd30</load_address>
         <run_address>0xd30</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x8dd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x8dd</load_address>
         <run_address>0x8dd</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_line</name>
         <load_address>0x9a4</load_address>
         <run_address>0x9a4</run_address>
         <size>0x49</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_line</name>
         <load_address>0x9ed</load_address>
         <run_address>0x9ed</run_address>
         <size>0x3b1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_line</name>
         <load_address>0xd9e</load_address>
         <run_address>0xd9e</run_address>
         <size>0x242</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_line</name>
         <load_address>0xfe0</load_address>
         <run_address>0xfe0</run_address>
         <size>0x336</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x1316</load_address>
         <run_address>0x1316</run_address>
         <size>0x15f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_line</name>
         <load_address>0x1475</load_address>
         <run_address>0x1475</run_address>
         <size>0x654</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_line</name>
         <load_address>0x1ac9</load_address>
         <run_address>0x1ac9</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_line</name>
         <load_address>0x1c41</load_address>
         <run_address>0x1c41</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_line</name>
         <load_address>0x33af</load_address>
         <run_address>0x33af</run_address>
         <size>0x982</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_line</name>
         <load_address>0x3d31</load_address>
         <run_address>0x3d31</run_address>
         <size>0x176</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x3ea7</load_address>
         <run_address>0x3ea7</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_line</name>
         <load_address>0x4083</load_address>
         <run_address>0x4083</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_line</name>
         <load_address>0x459d</load_address>
         <run_address>0x459d</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_line</name>
         <load_address>0x45db</load_address>
         <run_address>0x45db</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x46d9</load_address>
         <run_address>0x46d9</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x4799</load_address>
         <run_address>0x4799</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_line</name>
         <load_address>0x4961</load_address>
         <run_address>0x4961</run_address>
         <size>0x69</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_line</name>
         <load_address>0x49ca</load_address>
         <run_address>0x49ca</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_line</name>
         <load_address>0x4a6e</load_address>
         <run_address>0x4a6e</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_loc</name>
         <load_address>0x1a3a</load_address>
         <run_address>0x1a3a</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_loc</name>
         <load_address>0x1e4e</load_address>
         <run_address>0x1e4e</run_address>
         <size>0x15b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_loc</name>
         <load_address>0x1fa9</load_address>
         <run_address>0x1fa9</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_loc</name>
         <load_address>0x2081</load_address>
         <run_address>0x2081</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_loc</name>
         <load_address>0x24a5</load_address>
         <run_address>0x24a5</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_loc</name>
         <load_address>0x2611</load_address>
         <run_address>0x2611</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_loc</name>
         <load_address>0x2680</load_address>
         <run_address>0x2680</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_loc</name>
         <load_address>0x27e7</load_address>
         <run_address>0x27e7</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x10b0</size>
         <contents>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-96"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x11c0</load_address>
         <run_address>0x11c0</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-178"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x1170</load_address>
         <run_address>0x1170</run_address>
         <size>0x50</size>
         <contents>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-109"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-141"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202001e0</run_address>
         <size>0xf</size>
         <contents>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-ce"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x1e0</size>
         <contents>
            <object_component_ref idref="oc-cf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-17c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-138" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-139" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13a" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13b" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13c" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13d" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13f" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-15b" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1319</size>
         <contents>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-17e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15d" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9a6e</size>
         <contents>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-17d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15f" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6a0</size>
         <contents>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-7e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-161" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7252</size>
         <contents>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-d3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-163" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd60</size>
         <contents>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-9a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-165" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4b0e</size>
         <contents>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-7b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-167" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x280d</size>
         <contents>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-9c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-171" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-7d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-17b" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-184" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x11f0</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-185" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x1ef</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-186" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x11f0</used_space>
         <unused_space>0x1ee10</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x10b0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1170</start_address>
               <size>0x50</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x11c0</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x11f0</start_address>
               <size>0x1ee10</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x3ef</used_space>
         <unused_space>0x7c11</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-13d"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-13f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1e0</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202001e0</start_address>
               <size>0xf</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001ef</start_address>
               <size>0x7c11</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x11cc</load_address>
            <load_size>0xb</load_size>
            <run_address>0x202001e0</run_address>
            <run_size>0xf</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x11d8</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1e0</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x11e0</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x11f0</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x11f0</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x11c0</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x11cc</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-ac">
         <name>SYSCFG_DL_init</name>
         <value>0xf35</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-ad">
         <name>SYSCFG_DL_initPower</name>
         <value>0xaf1</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-ae">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x6fd</value>
         <object_component_ref idref="oc-b7"/>
      </symbol>
      <symbol id="sm-af">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xb99</value>
         <object_component_ref idref="oc-b8"/>
      </symbol>
      <symbol id="sm-b0">
         <name>SYSCFG_DL_MotorFront_init</name>
         <value>0x81d</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-b1">
         <name>SYSCFG_DL_MotorBack_init</name>
         <value>0x791</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-b2">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x112d</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-bd">
         <name>Default_Handler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-be">
         <name>Reset_Handler</name>
         <value>0x1161</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-bf">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-c0">
         <name>NMI_Handler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c1">
         <name>HardFault_Handler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c2">
         <name>SVC_Handler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c3">
         <name>PendSV_Handler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c4">
         <name>GROUP0_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c5">
         <name>GROUP1_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c6">
         <name>TIMG8_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c7">
         <name>UART3_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c8">
         <name>ADC0_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c9">
         <name>ADC1_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ca">
         <name>CANFD0_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cb">
         <name>DAC0_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cc">
         <name>SPI0_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cd">
         <name>SPI1_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ce">
         <name>UART1_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cf">
         <name>UART2_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d0">
         <name>UART0_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d1">
         <name>TIMG0_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d2">
         <name>TIMG6_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d3">
         <name>TIMA0_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d4">
         <name>TIMA1_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d5">
         <name>TIMG7_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d6">
         <name>TIMG12_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d7">
         <name>I2C0_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d8">
         <name>I2C1_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d9">
         <name>AES_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-da">
         <name>RTC_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-db">
         <name>DMA_IRQHandler</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e4">
         <name>main</name>
         <value>0xe89</value>
         <object_component_ref idref="oc-6c"/>
      </symbol>
      <symbol id="sm-102">
         <name>Task_Init</name>
         <value>0xa95</value>
         <object_component_ref idref="oc-89"/>
      </symbol>
      <symbol id="sm-103">
         <name>Task_Motor</name>
         <value>0xd55</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-104">
         <name>Data_MotorPWM</name>
         <value>0x202001e0</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-105">
         <name>Task_Key</name>
         <value>0x8a9</value>
         <object_component_ref idref="oc-c8"/>
      </symbol>
      <symbol id="sm-106">
         <name>Task_LED</name>
         <value>0xd1d</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-107">
         <name>Flag_LED</name>
         <value>0x202001ec</value>
         <object_component_ref idref="oc-11d"/>
      </symbol>
      <symbol id="sm-114">
         <name>Key_Read</name>
         <value>0xdc1</value>
         <object_component_ref idref="oc-117"/>
      </symbol>
      <symbol id="sm-127">
         <name>Motor_Start</name>
         <value>0xc25</value>
         <object_component_ref idref="oc-be"/>
      </symbol>
      <symbol id="sm-128">
         <name>Motor_SetPWM</name>
         <value>0x585</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-135">
         <name>SysTick_Handler</name>
         <value>0xe19</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-136">
         <name>uwTick</name>
         <value>0x202001e8</value>
         <object_component_ref idref="oc-5b"/>
      </symbol>
      <symbol id="sm-137">
         <name>delayTick</name>
         <value>0x202001e4</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-138">
         <name>Sys_GetTick</name>
         <value>0x113d</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-14e">
         <name>Task_IdleFunction</name>
         <value>0xca3</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-14f">
         <name>Task_Add</name>
         <value>0x649</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-150">
         <name>Task_Start</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-8e"/>
      </symbol>
      <symbol id="sm-151">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-152">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-153">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-154">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-155">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-156">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-157">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-158">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-159">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-162">
         <name>DL_Common_delayCycles</name>
         <value>0x1149</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-179">
         <name>DL_Timer_setClockConfig</name>
         <value>0xf19</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-17a">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x111d</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-17b">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0xefd</value>
         <object_component_ref idref="oc-105"/>
      </symbol>
      <symbol id="sm-17c">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1029</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-17d">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x3a5</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-18e">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0x4a9</value>
         <object_component_ref idref="oc-f8"/>
      </symbol>
      <symbol id="sm-18f">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0xbe1</value>
         <object_component_ref idref="oc-fa"/>
      </symbol>
      <symbol id="sm-190">
         <name>DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <value>0xa31</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-199">
         <name>qsort</name>
         <value>0x271</value>
         <object_component_ref idref="oc-112"/>
      </symbol>
      <symbol id="sm-1a4">
         <name>_c_int00_noargs</name>
         <value>0xe41</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-1a5">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-1b1">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xce1</value>
         <object_component_ref idref="oc-9d"/>
      </symbol>
      <symbol id="sm-1b9">
         <name>_system_pre_init</name>
         <value>0x1165</value>
         <object_component_ref idref="oc-68"/>
      </symbol>
      <symbol id="sm-1c4">
         <name>__TI_zero_init_nomemset</name>
         <value>0x106f</value>
         <object_component_ref idref="oc-4b"/>
      </symbol>
      <symbol id="sm-1cd">
         <name>__TI_decompress_none</name>
         <value>0x10fb</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-1d8">
         <name>__TI_decompress_lzss</name>
         <value>0x9b5</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-1e2">
         <name>abort</name>
         <value>0x1169</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-1e3">
         <name>C$$EXIT</name>
         <value>0x1168</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-1eb">
         <name>__aeabi_memcpy</name>
         <value>0x1155</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-1ec">
         <name>__aeabi_memcpy4</name>
         <value>0x1155</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-1ed">
         <name>__aeabi_memcpy8</name>
         <value>0x1155</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-1f6">
         <name>TI_memcpy_small</name>
         <value>0x10e9</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-1f7">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1fa">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1fb">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
