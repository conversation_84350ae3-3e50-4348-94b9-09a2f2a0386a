#ifndef __PID_h
#define __PID_h

#include "SysConfig.h"

typedef struct
{
    float Kp; //比例系数
    float Ki; //积分系数
    float Kd; //微分系数
    float Acutal_Now; //当前实际值
    float Acutal_Last; //上一次实际值
    float Target; //目标值
    float Out; //输出值
    float Dif_Out; //微分项输出值
    float Err_Now; //当前误差
    float Err_Last; //上次误差
    float Err_Int; //误差累积
} PID_Def_t;

void PID_Init(PID_Def_t *pid);
void PID_Prosc(PID_Def_t *pid);
void PID_SetParams(PID_Def_t *pid, float kp, float ki, float kd);

#endif
