[{"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/APP/Src/Interrupt.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/APP/Src/Task_App.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/ADC.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/Key_Led.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/MPU6050.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/Motor.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/No_Mcu_Ganv_Grayscale_Sensor.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/OLED.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/OLED_Font.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/PID.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/PID_IQMath.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/Serial.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/SysTick.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/Task.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/BSP/Src/Tracker.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/DMP/inv_mpu.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/DMP/inv_mpu_dmp_motion_driver.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/Debug/ti_msp_dl_config.c"}, {"directory": "C:/Users/<USER>/Desktop/fan/fan/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/fan/fan\" -I\"C:/Users/<USER>/Desktop/fan/fan/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/fan/fan/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/APP/Inc\" -I\"C:/Users/<USER>/Desktop/fan/fan/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/fan/fan/main.c"}]