 .intvecs .text .const .cinit .pinit .rodata .ARM.exidx .init_array .binit .TI.ramfunc .vtable .args .data .bss .sysmem .stack .BCRConfig .BSLConfig .TI.noinit .TI.persistent .TI.local .TI.onchip .TI.offchip __llvm_prf_cnts __llvm_prf_bits .debug_info .debug_frame .debug_line Veneer$$CMSE ti_msp_dl_config.c $t.0 $t.1 DL_GPIO_reset DL_Timer_reset DL_I2C_reset DL_UART_reset DL_MathACL_reset DL_GPIO_enablePower DL_Timer_enablePower DL_I2C_enablePower DL_UART_enablePower DL_MathACL_enablePower $d.2 $t.3 DL_GPIO_initPeripheralAnalogFunction DL_GPIO_initPeripheralOutputFunction DL_GPIO_enableOutput DL_GPIO_initPeripheralInputFunctionFeatures DL_GPIO_enableHiZ DL_GPIO_initPeripheralInputFunction DL_GPIO_initDigitalOutput DL_GPIO_initDigitalInputFeatures DL_GPIO_clearPins DL_GPIO_setUpperPinsPolarity DL_GPIO_clearInterruptStatus DL_GPIO_enableInterrupt DL_GPIO_setPins $d.4 $t.5 DL_SYSCTL_setBORThreshold DL_SYSCTL_setFlashWaitState DL_SYSCTL_setSYSOSCFreq DL_SYSCTL_disableHFXT DL_SYSCTL_disableSYSPLL DL_SYSCTL_setULPCLKDivider __NVIC_SetPriority $d.6 gSYSPLLConfig $t.7 DL_Timer_setCounterControl DL_Timer_enableClock DL_Timer_setCCPDirection $d.8 gMotorFrontClockConfig gMotorFrontConfig $t.9 $d.10 gMotorBackClockConfig gMotorBackConfig $t.11 DL_I2C_setAnalogGlitchFilterPulseWidth DL_I2C_enableAnalogGlitchFilter DL_I2C_resetControllerTransfer DL_I2C_setTimerPeriod DL_I2C_setControllerTXFIFOThreshold DL_I2C_setControllerRXFIFOThreshold DL_I2C_enableControllerClockStretching DL_I2C_enableController $d.12 gI2C_MPU6050ClockConfig $t.13 DL_I2C_enableInterrupt $d.14 gI2C_OLEDClockConfig $t.15 DL_UART_setOversampling DL_UART_setBaudRateDivisor DL_UART_enableInterrupt DL_UART_enableDMAReceiveEvent DL_UART_enableDMATransmitEvent DL_UART_enableFIFOs DL_UART_setRXFIFOThreshold DL_UART_setTXFIFOThreshold DL_UART_setRXInterruptTimeout DL_UART_enable $d.16 gUART0ClockConfig gUART0Config $t.17 $t.18 SysTick_Config $d.19 $t.20 $d.21 $t.22 $d.23 $t.24 $d.25 $t.26 $d.27 $t.28 $d.29 $t.30 $d.31 $t.32 $d.33 $t.34 $d.35 $t.36 $d.37 $t.38 $d.39 $t.40 $d.41 $t.42 $d.43 $t.44 $d.45 $t.46 $d.47 $t.48 $d.49 $t.50 $d.51 $t.52 $d.53 $t.54 $d.55 $t.56 $t.57 $t.58 $d.59 $t.60 $d.61 $t.62 $d.63 $t.64 $d.65 $t.66 DL_Common_updateReg $d.67 $t.68 $d.69 $t.70 $d.71 $t.72 $d.73 $t.74 $d.75 $t.76 $d.77 $t.78 $d.79 $t.80 $d.81 $t.82 $t.83 $t.84 $t.85 $d.86 $t.87 $d.88 $t.89 $d.90 $t.91 $d.92 $t.93 $d.94 $t.95 $d.96 $t.97 $d.98 $t.99 $t.100 $d.101 $t.102 $d.103 $t.104 $d.105 $t.106 $d.107 $t.108 $t.109 $d.110 $t.111 $d.112 $t.113 $d.114 $t.115 $t.116 DL_DMA_clearInterruptStatus DL_DMA_enableInterrupt $d.117 gDMA_CH_RXConfig $t.118 $d.119 $t.120 $d.121 $t.122 $d.123 gDMA_CH_TXConfig $t.124 $d.125 $t.126 startup_mspm0g350x_ticlang.c main.c $d.1 Interrupt.c DL_Interrupt_getPendingGroup DL_GPIO_getEnabledInterruptStatus DL_GPIO_readPins __NVIC_EnableIRQ Task_App.c $t.2 $d.3 $t.4 $d.5 Task_Key.Key_Old $t.6 $d.7 $t.8 Task_IdleFunction.CNT $t.14 $d.15 Key_Led.c MPU6050.c mspm0_i2c_disable mspm0_i2c_enable $d.9 $t.10 DL_I2C_transmitControllerData DL_I2C_clearInterruptStatus DL_I2C_getControllerStatus DL_I2C_startControllerTransfer DL_I2C_getRawInterruptStatus $d.11 $t.12 $d.18 $t.19 $d.20 $t.21 DL_I2C_isControllerRXFIFOEmpty DL_I2C_receiveControllerData $d.22 $t.23 $d.24 $t.25 $d.26 $t.27 DL_I2C_getSDAStatus inv_orientation_matrix_to_scalar $d.28 hal gyro_orientation tap_cb android_orient_cb $t.29 $d.30 $t.31 inv_row_2_scale $t.33 $t.51 Motor.c DL_Timer_startCounter OLED.c $t.16 $d.17 OLED_Font.c PID.c PID_IQMath.c Serial.c DL_DMA_setSrcAddr DL_DMA_setDestAddr DL_DMA_setTransferSize DL_DMA_enableChannel DL_DMA_disableChannel SysTick.c Task.c Task_Num Task_Schedule Task_CMP Tracker.c inv_mpu.c st $d.13 set_int_enable $t.86 $d.87 $t.96 $d.97 $t.98 $d.99 inv_mpu_dmp_motion_driver.c dmp_memory dmp $d.32 $d.34 $t.39 decode_gesture $d.40 $t.41 $d.42 $t.43 $d.44 $t.45 $d.46 _IQNdiv.c _IQNmpy.c _IQNtables.c _IQNtoF.c dl_common.c dl_dma.c dl_i2c.c dl_timer.c $t.47 $d.48 $t.55 $d.56 $t.75 $d.76 dl_uart.c dl_sysctl_mspm0g1x0x_g3x0x.c vsnprintf.c vsprintf.c _outs _outc e_asin.c OUTLINED_FUNCTION_0 OUTLINED_FUNCTION_2 OUTLINED_FUNCTION_3 OUTLINED_FUNCTION_4 OUTLINED_FUNCTION_1 e_atan2.c e_sqrt.c s_atan.c atanhi atanlo aeabi_portable.c assert.c defs.c memcmp.c memory.c qsort.c boot_cortex_m.c $Tramp$TT$L$PI$$_c_int00_noargs $t $d mathacl_init.c autoinit.c pre_init.c cpy_tbl.c copy_zero_init.c copy_decompress_none.c copy_decompress_lzss.c _printfi.c strlen pproc_fflags___TI_printfi _pproc_fwp___TI_printfi _pproc_str___TI_printfi _setfield___TI_printfi _pproc_wstr___TI_printfi _pproc_diouxp___TI_printfi _pconv_a _pconv_g _pconv_f _pconv_e _getarg_diouxp___TI_printfi _ltostr___TI_printfi _div___TI_printfi fcvt _fcpy _mcpy _ecpy strchr sprintf.c fflush.c fputs.c _io_perm.c setvbuf.c wcslen.c s_frexp.c s_scalbn.c exit.c _lock.c _ltoa.c args_main.c atoi.c memccpy.c fopen.c fseek.c fclose.c write.c host_device.c remove.c open.c lseek.c close.c unlink.c hostclose.c hostlseek.c hostopen.c hostread.c hostrename.c hostunlink.c hostwrite.c trgmsg.c $Tramp$TT$L$PI$$__aeabi_dsub $Tramp$TT$L$PI$$__aeabi_dadd $Tramp$TT$L$PI$$__aeabi_dmul $Tramp$TT$L$PI$$__aeabi_ddiv comparedf2.c divmoddi4.c aeabi_div0.c getdevice.c _loop _ret .debug_loc .debug_ranges .debug_pubnames .debug_pubtypes __start___llvm_prf_cnts __stop___llvm_prf_cnts __start___llvm_prf_bits __stop___llvm_prf_bits __TI_CINIT_Base __TI_CINIT_Limit __TI_CINIT_Warm __TI_Handler_Table_Base __TI_Handler_Table_Limit binit __binit__ __STACK_SIZE __STACK_END __TI_pprof_out_hndl __TI_prof_data_start __TI_prof_data_size SYSCFG_DL_init SYSCFG_DL_initPower SYSCFG_DL_GPIO_init SYSCFG_DL_SYSCTL_init SYSCFG_DL_MotorFront_init SYSCFG_DL_MotorBack_init SYSCFG_DL_I2C_MPU6050_init SYSCFG_DL_I2C_OLED_init SYSCFG_DL_UART0_init SYSCFG_DL_DMA_init SYSCFG_DL_SYSTICK_init SYSCFG_DL_DMA_CH_RX_init SYSCFG_DL_DMA_CH_TX_init Default_Handler Reset_Handler interruptVectors NMI_Handler HardFault_Handler SVC_Handler PendSV_Handler GROUP0_IRQHandler TIMG8_IRQHandler UART3_IRQHandler ADC0_IRQHandler ADC1_IRQHandler CANFD0_IRQHandler DAC0_IRQHandler SPI0_IRQHandler SPI1_IRQHandler UART1_IRQHandler UART2_IRQHandler UART0_IRQHandler TIMG0_IRQHandler TIMG6_IRQHandler TIMA0_IRQHandler TIMA1_IRQHandler TIMG7_IRQHandler TIMG12_IRQHandler I2C0_IRQHandler I2C1_IRQHandler AES_IRQHandler RTC_IRQHandler DMA_IRQHandler main SysTick_Handler GROUP1_IRQHandler ExISR_Flag Flag_MPU6050_Ready Interrupt_Init enable_group1_irq Task_Init Task_Motor Data_MotorPWM_Duty Task_Key Task_LED Task_Serial Task_OLED Data_MotorPID Flag_LED Data_MotorEncoder Task_IdleFunction Key_Read mpu6050_i2c_sda_unlock mspm0_i2c_write mspm0_i2c_read MPU6050_Init Read_Quad more sensors Data_Gyro Data_Accel quat sensor_timestamp Data_Pitch Data_Roll Data_Yaw Motor_Start Motor_SetPWM Motor_SetDirc I2C_OLED_i2c_sda_unlock I2C_OLED_WR_Byte I2C_OLED_Set_Pos I2C_OLED_Clear OLED_ShowChar OLED_ShowString OLED_Printf OLED_Init asc2_0806 asc2_1608 PID_Init PID_Prosc Serial_Init Serial_RxData SysTick_Increasment uwTick delayTick Sys_GetTick SysGetTick Delay Task_Add Task_Start mpu_init mpu_set_gyro_fsr mpu_set_accel_fsr mpu_set_lpf mpu_set_sample_rate mpu_configure_fifo mpu_set_bypass mpu_set_sensors mpu_lp_accel_mode mpu_reset_fifo mpu_set_int_latched mpu_get_gyro_fsr mpu_get_accel_fsr mpu_get_sample_rate mpu_read_fifo_stream mpu_set_dmp_state test mpu_write_mem mpu_read_mem mpu_load_firmware reg hw dmp_load_motion_driver_firmware dmp_set_orientation dmp_set_fifo_rate dmp_set_tap_thresh dmp_set_tap_axes dmp_set_tap_count dmp_set_tap_time dmp_set_tap_time_multi dmp_set_shake_reject_thresh dmp_set_shake_reject_time dmp_set_shake_reject_timeout dmp_enable_feature dmp_enable_gyro_cal dmp_enable_lp_quat dmp_enable_6x_lp_quat dmp_read_fifo dmp_register_tap_cb dmp_register_android_orient_cb __TI_ATRegion0_src_addr __TI_ATRegion0_trg_addr __TI_ATRegion0_region_sz __TI_ATRegion1_src_addr __TI_ATRegion1_trg_addr __TI_ATRegion1_region_sz __TI_ATRegion2_src_addr __TI_ATRegion2_trg_addr __TI_ATRegion2_region_sz DL_Common_delayCycles DL_DMA_initChannel DL_I2C_setClockConfig DL_I2C_fillControllerTXFIFO DL_I2C_flushControllerTXFIFO DL_Timer_setClockConfig DL_Timer_setCaptureCompareValue DL_Timer_setCaptCompUpdateMethod DL_Timer_setCaptureCompareOutCtl DL_Timer_initFourCCPWMMode DL_UART_init DL_UART_setClockConfig DL_SYSCTL_configSYSPLL DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK DL_SYSCTL_setHFCLKSourceHFXTParams vsprintf asin asinl atan2 atan2l sqrt sqrtl atan atanl __aeabi_errno_addr __aeabi_errno memcmp qsort _c_int00_noargs __stack __TI_auto_init_nobinit_nopinit _system_pre_init __TI_zero_init_nomemset __TI_decompress_none __TI_decompress_lzss __TI_printfi wcslen frexp frexpl scalbn ldexp scalbnl ldexpl abort C$$EXIT __TI_ltoa atoi memccpy __aeabi_ctype_table_ __aeabi_ctype_table_C __aeabi_fadd __addsf3 __aeabi_fsub __subsf3 __aeabi_dadd __adddf3 __aeabi_dsub __subdf3 __aeabi_dmul __muldf3 __muldsi3 __aeabi_fmul __mulsf3 __aeabi_fdiv __divsf3 __aeabi_ddiv __divdf3 __aeabi_f2d __extendsfdf2 __aeabi_d2iz __fixdfsi __aeabi_f2iz __fixsfsi __aeabi_i2d __floatsidf __aeabi_i2f __floatsisf __aeabi_ui2f __floatunsisf __aeabi_lmul __muldi3 __aeabi_d2f __truncdfsf2 __aeabi_dcmpeq __aeabi_dcmplt __aeabi_dcmple __aeabi_dcmpge __aeabi_dcmpgt __aeabi_fcmpeq __aeabi_fcmplt __aeabi_fcmple __aeabi_fcmpge __aeabi_fcmpgt __aeabi_idiv __aeabi_idivmod __aeabi_memcpy __aeabi_memcpy4 __aeabi_memcpy8 __aeabi_memset __aeabi_memset4 __aeabi_memset8 __aeabi_uidiv __aeabi_uidivmod __aeabi_uldivmod __eqsf2 __lesf2 __ltsf2 __nesf2 __cmpsf2 __gtsf2 __gesf2 __udivmoddi4 __aeabi_llsl __ashldi3 __ledf2 __gedf2 __cmpdf2 __eqdf2 __ltdf2 __nedf2 __gtdf2 __aeabi_idiv0 __aeabi_ldiv0 TI_memcpy_small TI_memset_small __TI_static_base__ __mpu_init _system_post_cinit 