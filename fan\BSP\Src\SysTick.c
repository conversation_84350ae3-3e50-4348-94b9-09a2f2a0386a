#include "SysTick.h"

volatile uint32_t uwTick = 0;
volatile uint32_t delayTick = 0;

/**
 * @brief systick毫秒设置
 * 
 */
void SysTick_Increasment(void)
{
    uwTick++;
    if (delayTick) delayTick--;
}

/**
 * @brief 获取时基
 * 
 * @return uint32_t uwTick
 */
uint32_t Sys_GetTick(void)
{
    return uwTick;
}

/**
 * @brief 获取时间戳
 * 
 * @param timestamp 
 * @return uint32_t 
 * @note 移植DMP库需要的函数
 */
uint32_t SysGetTick(uint32_t *timestamp)
{
    *timestamp = Sys_GetTick();
    return *timestamp;
}

/**
 * @brief 微秒级延时
 * 
 * @param xms 延时时间
 */
void Delay(uint32_t xms)
{
    delayTick = xms;
    while (delayTick)
    {
    }
}